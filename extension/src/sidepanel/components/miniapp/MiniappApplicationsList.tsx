import React, { useState, useMemo } from 'react';
import { Dropdown, Tooltip, Modal } from 'antd';
import type { MenuProps } from 'antd';
import { MiniApp } from '@the-agent/shared';
import { Archive, Trash2, X } from 'lucide-react';
import activateIcon from '~/assets/icons/activate.svg';
import dotIcon from '~/assets/icons/dot.svg';
import newminiappIcon from '~/assets/icons/newchat.svg';
import archiveIcon from '~/assets/icons/archive.svg';
import { getAvatarColor, getInitials } from '~/utils/profile';
import { useLanguage } from '~/utils/i18n';

type FilterType = 'All' | 'Installed' | 'Uninstalled';

interface MiniappApplicationsListProps {
  miniapps: MiniApp[];
  onSelectMiniapp: (miniapp: MiniApp) => void;
  onNewProject: () => void;
  onArchiveMiniapp?: (miniapp: MiniApp) => void;
  onDeleteMiniapp?: (miniapp: MiniApp) => void;
  onActivateMiniapp?: (miniapp: MiniApp) => void;
  onShowArchived?: () => void;
  isArchivedView?: boolean;
  onClose?: () => void;
  filter?: FilterType;
  onFilterChange?: (filter: FilterType) => void;
}

const MiniappApplicationsList: React.FC<MiniappApplicationsListProps> = ({
  miniapps,
  onSelectMiniapp,
  onNewProject,
  onArchiveMiniapp,
  onDeleteMiniapp,
  onActivateMiniapp,
  onShowArchived,
  isArchivedView = false,
  onClose,
  filter = 'All',
  onFilterChange,
}) => {
  const { getMessage } = useLanguage();
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [miniappToArchive, setMiniappToArchive] = useState<MiniApp | null>(null);

  // Filter dropdown items
  const filterItems = [
    { key: 'All', label: getMessage('filterAll') },
    { key: 'Installed', label: getMessage('filterInstalled') },
    { key: 'Uninstalled', label: getMessage('filterUninstalled') },
  ];

  const filterLabel = useMemo(() => {
    switch (filter) {
      case 'All':
        return getMessage('filterAll');
      case 'Installed':
        return getMessage('filterInstalled');
      case 'Uninstalled':
        return getMessage('filterUninstalled');
      default:
        return filter;
    }
  }, [filter, getMessage]);

  // Handle archive confirmation
  const handleArchiveClick = (miniapp: MiniApp) => {
    setMiniappToArchive(miniapp);
    setShowArchiveDialog(true);
  };

  const handleArchiveConfirm = () => {
    if (miniappToArchive && onArchiveMiniapp) {
      onArchiveMiniapp(miniappToArchive);
    }
    setShowArchiveDialog(false);
    setMiniappToArchive(null);
  };

  const handleArchiveCancel = () => {
    setShowArchiveDialog(false);
    setMiniappToArchive(null);
  };

  // Dot menu items
  const getDotMenuItems = (_miniapp: MiniApp): MenuProps['items'] => {
    if (isArchivedView) {
      return [
        {
          key: 'activate',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <img src={activateIcon} alt="Activate" style={{ width: 18, height: 18 }} />
              {getMessage('activate')}
            </div>
          ),
        },
        {
          key: 'delete',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Trash2 size={16} />
              {getMessage('delete')}
            </div>
          ),
        },
      ];
    } else {
      return [
        {
          key: 'archive',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Archive size={16} />
              {getMessage('archive')}
            </div>
          ),
        },
        {
          key: 'delete',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Trash2 size={16} />
              {getMessage('delete')}
            </div>
          ),
        },
      ];
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#ffffff',
        borderRadius: '0 16px 16px 0',
        boxShadow: '0 0 15px 0 rgba(0, 0, 0, 0.15)',
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 16px',
          height: '44px',
          borderBottom: '1px solid #f0f0f0',
        }}
      >
        <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
          {isArchivedView ? getMessage('archived') : getMessage('miniapp')}
        </h2>
        <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
          {!isArchivedView && (
            // TODO Modify newProject Click Event, refer to tooltipNewChat
            // 1. No longer by popping up modal newProject
            // 2. After clicking, create a new miniapp directly.
            // 3. After creation, jump directly to the miniapp details page.
            <Tooltip title={getMessage('newProject')} placement="bottom">
              <button
                onClick={() => {
                  if (onClose) onClose();
                  // Small delay to allow slide-out animation before showing modal
                  setTimeout(() => {
                    onNewProject();
                  }, 200);
                }}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: '#6b7280',
                  cursor: 'pointer',
                  transition: 'background 0.2s',
                }}
                onMouseOver={e => {
                  e.currentTarget.style.backgroundColor = '#E5E7EB';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <img
                  src={newminiappIcon}
                  alt={getMessage('newProject')}
                  style={{ width: 20, height: 20 }}
                />
              </button>
            </Tooltip>
          )}
          <Tooltip title={getMessage('tooltipClose')} placement="bottom">
            <button
              onClick={() => onClose && onClose()}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Pin top bar */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px 16px 0px 16px',
          backgroundColor: '#ffffff',
        }}
      >
        {/* Filter dropdown */}
        <Dropdown
          menu={{
            items: filterItems,
            onClick: ({ key }) => onFilterChange?.(key as FilterType),
          }}
          trigger={['click']}
        >
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 12px',
              borderRadius: '6px',
              border: '0px solid #d1d5db',
              backgroundColor: '#ffffff',
              color: '#374151',
              fontSize: '14px',
              cursor: 'pointer',
              transition: 'all 0.2s',
            }}
          >
            {filterLabel}
            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" strokeWidth="1.5" fill="none" />
            </svg>
          </button>
        </Dropdown>

        {/* Action buttons */}
        <div style={{ display: 'flex', gap: '12px' }}>
          <Tooltip title={getMessage('archived')}>
            <button
              onClick={() => {
                if (onClose) onClose();
                // Small delay to allow slide-out animation before showing archived view
                setTimeout(() => {
                  onShowArchived?.();
                }, 200);
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                border: 'none',
                backgroundColor: 'transparent',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f3f4f6';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <img src={archiveIcon} alt="Archive" style={{ width: 18, height: 18 }} />
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Miniapp list */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '16px',
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          {miniapps.map(miniapp => (
            <div
              key={miniapp.id}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: '12px',
                padding: '12px',
                borderRadius: '8px',
                backgroundColor: '#F9F9F9',
                cursor: 'pointer',
                transition: 'all 0.2s',
                border: isArchivedView ? '1px dashed #9ca3af' : '1px solid transparent',
                background: '#F9F9F9',
              }}
              onClick={() => onSelectMiniapp(miniapp)}
              onMouseOver={e => {
                e.currentTarget.style.background =
                  'linear-gradient(#F9F9F9, #F9F9F9) padding-box, linear-gradient(90deg, #723DF6, #0081E4) border-box';
                e.currentTarget.style.border = '1px solid transparent';
              }}
              onMouseOut={e => {
                e.currentTarget.style.background = '#F9F9F9';
                e.currentTarget.style.border = isArchivedView
                  ? '1px dashed #9ca3af'
                  : '1px solid transparent';
              }}
            >
              {/* Avatar and Name*/}
              <div
                style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '12px' }}
              >
                <div
                  style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '8px',
                    backgroundColor: getAvatarColor(miniapp.name),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#ffffff',
                    fontSize: '14px',
                    fontWeight: 600,
                    flexShrink: 0,
                  }}
                >
                  {getInitials(miniapp.name)}
                </div>

                <div
                  style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#111827',
                    marginBottom: '2px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {miniapp.name}
                </div>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {miniapp.installation && miniapp.installation.code ? (
                  <Tooltip title={getMessage('installed')}>
                    <svg
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M12 15L7 10H10V3H14V10H17L12 15Z" fill="currentColor" />
                      <path
                        d="M20 15V18C20 19.1 19.1 20 18 20H6C4.9 20 4 19.1 4 18V15H6V18H18V15H20Z"
                        fill="currentColor"
                      />
                    </svg>
                  </Tooltip>
                ) : null}

                {/* Dot menu */}
                <div onClick={e => e.stopPropagation()}>
                  <Dropdown
                    menu={{
                      items: getDotMenuItems(miniapp),
                      onClick: ({ key, domEvent }) => {
                        domEvent?.stopPropagation();
                        if (key === 'archive') {
                          handleArchiveClick(miniapp);
                        } else if (key === 'delete') {
                          onDeleteMiniapp?.(miniapp);
                        } else if (key === 'activate') {
                          onActivateMiniapp?.(miniapp);
                        }
                      },
                    }}
                    trigger={['click']}
                    placement="bottomRight"
                  >
                    <button
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '24px',
                        height: '24px',
                        borderRadius: '4px',
                        border: 'none',
                        backgroundColor: 'transparent',
                        cursor: 'pointer',
                        transition: 'background 0.2s',
                      }}
                      onClick={e => e.stopPropagation()}
                      onMouseOver={e => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                      }}
                      onMouseOut={e => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <img src={dotIcon} alt="More" style={{ width: 16, height: 16 }} />
                    </button>
                  </Dropdown>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Archive Confirmation Dialog */}
      <Modal
        title={
          <div style={{ textAlign: 'center', fontSize: '20px', fontWeight: 600, color: '#111827' }}>
            {getMessage('archivingTitle')}
          </div>
        }
        open={showArchiveDialog}
        onCancel={handleArchiveCancel}
        footer={null}
        centered
        width={300}
        closable={false}
      >
        <div style={{ textAlign: 'center' }}>
          <p
            style={{
              fontSize: '14px',
              color: '#374151',
              lineHeight: '1.6',
              fontWeight: 400,
            }}
          >
            {getMessage('archiveConfirm1')}
            <br />
            {getMessage('archiveConfirm2')}
            <br />
            {getMessage('archiveConfirm3')}
          </p>

          {/* Custom Buttons */}
          <div style={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
            <button
              onClick={handleArchiveCancel}
              style={{
                padding: '12px 32px',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                backgroundColor: '#ffffff',
                color: '#374151',
                fontSize: '16px',
                fontWeight: 500,
                cursor: 'pointer',
                minWidth: '100px',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#9ca3af';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            >
              {getMessage('no')}
            </button>
            <button
              onClick={handleArchiveConfirm}
              style={{
                padding: '12px 32px',
                borderRadius: '8px',
                border: 'none',
                backgroundColor: '#374151',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 500,
                cursor: 'pointer',
                minWidth: '100px',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#1f2937';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#374151';
              }}
            >
              {getMessage('yes')}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MiniappApplicationsList;
